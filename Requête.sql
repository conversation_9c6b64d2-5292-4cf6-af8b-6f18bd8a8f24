-- SQLite
SELECT * from employes where departement = 'Informatique';

SELECT * from employes ORDER BY salaire DESC;

SELECT * from employes ORDER BY salaire DESC LIMIT 5;

SELECT * from employes ORDER BY salaire DESC;
SELECT AVG(salaire) from employes;

SELECT * from employes Order BY departement;
SELECT Count(*), departement from employes GROUP BY departement;


SELECT * from employes Order BY departement;
SELECT * from employes where departement = 'Informatique' AND salaire > 50000;

SELECT * from employes Order BY prenom;
SELECT * from employes where prenom LIKE 'C%';

SELECT * from employes Order BY departement;
SELECT departement, Count(*) as nombre, AVG(salaire) as salaire_moyen  from employes GROUP BY departement;

SELECT * from employes Order BY departement;
SELECT departement, Count(*) as nombre, AVG(salaire) as salaire_moyen  from employes GROUP BY departement where AVG(salaire) > 45000;