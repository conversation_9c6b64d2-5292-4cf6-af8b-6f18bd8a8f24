-- SQLite
SELECT * from employes where departement = 'Informatique';

SELECT * from employes ORDER BY salaire DESC;

SELECT * from employes ORDER BY salaire DESC LIMIT 5;

SELECT * from employes ORDER BY salaire DESC;
SELECT AVG(salaire) from employes;

SELECT * from employes Order BY departement;
SELECT Count(*), departement from employes GROUP BY departement;


SELECT * from employes Order BY departement;
SELECT * from employes where departement = 'Informatique' AND salaire > 50000;

SELECT * from employes Order BY prenom;
SELECT * from employes where prenom LIKE 'C%';

SELECT * from employes Order BY departement;
SELECT departement, Count(*) as nombre, AVG(salaire) as salaire_moyen  from employes GROUP BY departement;

SELECT * from employes Order BY departement;
SELECT departement, Count(*) as nombre, AVG(salaire) as salaire_moyen  from employes GROUP BY departement HAVING AVG(salaire) > 45000;

SELECT * from employes ;
SELECT * from projets ;
SELECT nom, nom_projet from employes, projets where id_employe = id_chef_projet;  

-- Liste tous les employés et les projets qu'ils dirigent (inclut les employés sans projet)
SELECT e.nom, e.prenom, e.poste, e.departement, p.nom_projet, p.statut, p.budget
FROM employes e
LEFT JOIN projets p ON e.id_employe = p.id_chef_projet
ORDER BY e.nom, e.prenom;

SELECT e.nom, e.prenom, e.poste, e.departement, p.nom_projet, p.statut, p.budget
FROM employes e
SELF-JOIN employes X ON e.id_employe = X.id_chef_projet
ORDER BY e.nom, e.prenom;